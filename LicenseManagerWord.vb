﻿Imports System.IO
Imports System.Text
Imports System.Globalization ' 用于日期解析
Imports Microsoft.Win32 ' 用于访问注册表
Imports System.Security.Cryptography
Imports System.Net
Imports System.Net.Sockets
Imports System.Threading
Imports System.Threading.Tasks
Imports System.Net.Http
Imports Newtonsoft.Json
Imports Newtonsoft.Json.Linq
Imports Microsoft.SqlServer
Public Module LicenseManager
    ' --- 添加全局许可证状态变量 ---
    Public IsLicensed As Boolean = False ' 默认未授权

    ' --- 1. 嵌入公钥 ---
    Private Const PublicKeyXml As String = "<RSAKeyValue><Modulus>2CikPYisbNakUoAKcJliFvjlcm7GjOHG0eAlyUz0r+SPRP/Ol3S049hcb231+nGK4sg6PL69CjGDhg3wnHXSGSs7CHS4lIDvcwiqG+Vs0sx5HnJcLFNoYXI3kmPC2lqYlBfdIN0sYiIB8Y5RXQnri4VIAA58LC+zRjmHp7AasT+xw1qwct/oIiHSTi7Xn11V0M297xbkrYKonRKXqCpjc0eKaMBjkQBg9oQQfVo3O63wou+kGwAXxBDpptsrJb5jBF3TIuHWDbHr/1aCK9rlnFD1qxw/Rxg36w6GO4j2mS4LL2rDKaGaNYOes0qGXeST8qfSmnlvhBXwGGsR8Lp/mQ==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>"


    Private ReadOnly NtpServers As New List(Of String) From {
    "ntp.ntsc.ac.cn",      ' 中国科学院国家授时中心
    "cn.pool.ntp.org",     ' 中国 NTP 池 (主域名)
    "0.cn.pool.ntp.org",   ' 中国 NTP 池 (子域名 0)
    "1.cn.pool.ntp.org",   ' 中国 NTP 池 (子域名 1)
    "2.cn.pool.ntp.org",   ' 中国 NTP 池 (子域名 2)
    "3.cn.pool.ntp.org",   ' 中国 NTP 池 (子域名 3)
    "time.windows.com",    ' 微软全球时间服务器 (常用备用)
    "time.google.com",     ' 谷歌全球时间服务器 (常用备用)
    "pool.ntp.org"         ' 全球 NTP 池
    }

    ' --- 4. 存储/读取序列号的常量定义 ---
    Private Const RegistryBaseKey As String = "Software"
    Private Const CompanyName As String = "OPEN-AI"
    Private Const ProductName As String = "QTX_Word"
    Private Const LicenseValueName As String = "LicenseKey"

    ' --- 2. 核心验证函数 (未改变，但其调用的 GetNetworkTime 已被重写) ---
    Public Function ValidateLicense(serialNumber As String, ByRef errorMessage As String) As Boolean
        errorMessage = String.Empty

        ' --- a. 基本格式检查 ---
        If String.IsNullOrWhiteSpace(serialNumber) Then
            errorMessage = "序列号不能为空。"
            Return False
        End If
        If Not serialNumber.Contains("|"c) Then
            errorMessage = "序列号格式无效 (缺少分隔符 '|')。"
            Return False
        End If

        ' --- b. 解析序列号 ---
        Dim parts As String() = serialNumber.Split("|"c)
        If parts.Length <> 2 OrElse String.IsNullOrWhiteSpace(parts(0)) OrElse String.IsNullOrWhiteSpace(parts(1)) Then
            errorMessage = "序列号格式无效 (结构不正确)。"
            Return False
        End If
        Dim encodedLicenseData As String = parts(0)
        Dim encodedSignature As String = parts(1)

        Dim licenseDataBytes As Byte()
        Dim signatureBytes As Byte()
        Dim licenseData As String = ""

        ' --- c. Base64 解码 ---
        Try
            licenseDataBytes = Convert.FromBase64String(encodedLicenseData)
            signatureBytes = Convert.FromBase64String(encodedSignature)
        Catch ex As FormatException
            errorMessage = "序列号解码失败，可能已损坏。"
            Return False
        End Try

        ' --- d. 解码许可证数据字节 ---
        Try
            licenseData = Encoding.UTF8.GetString(licenseDataBytes)
        Catch ex As Exception
            errorMessage = "无法将许可证数据字节解码为文本。"
            Return False
        End Try
        If String.IsNullOrWhiteSpace(licenseData) Then
            errorMessage = "解码后的许可证数据为空。"
            Return False
        End If

        ' --- e. 解析许可证数据 ---
        Dim licensedMachineCode As String = ParseLicenseValue(licenseData, "MachineCode")
        Dim expiryString As String = ParseLicenseValue(licenseData, "Expiry")

        If licensedMachineCode Is Nothing OrElse expiryString Is Nothing Then
            errorMessage = "许可证数据不完整或格式错误 (缺少 MachineCode 或 Expiry)。"
            Return False
        End If

        ' --- f. 验证签名 (核心) ---
        Try
            Using rsa As New RSACryptoServiceProvider()
                rsa.FromXmlString(PublicKeyXml)
                Dim sha256Oid As String = CryptoConfig.MapNameToOID("SHA256")
                If Not rsa.VerifyData(licenseDataBytes, sha256Oid, signatureBytes) Then
                    errorMessage = "序列号签名无效。许可证可能被篡改或不匹配。"
                    Return False
                End If
            End Using
        Catch ex As CryptographicException
            errorMessage = "验证签名时发生加密错误: " & ex.Message
            Return False
        Catch ex As Exception
            errorMessage = "加载公钥或验证签名时发生意外错误: " & ex.Message
            Return False
        End Try

        ' --- g. 验证机器码 ---
        Dim currentMachineCode As String = HardwareInfoProvider.GetMachineCode()
        If currentMachineCode = "HARDWARE_INFO_ERROR" OrElse currentMachineCode = "NO_UNIQUE_ID_FOUND" OrElse currentMachineCode = "HASH_ERROR" Then
            errorMessage = "无法获取当前机器码进行验证 (错误: " & currentMachineCode & ")。"
            Return False
        End If
        If currentMachineCode = "Error" Then ' 兼容旧的错误返回
            errorMessage = "获取当前机器码时出错。"
            Return False
        End If
        If Not String.Equals(licensedMachineCode, currentMachineCode, StringComparison.OrdinalIgnoreCase) Then
            errorMessage = "序列号与当前机器不匹配。" & Environment.NewLine &
                           "预期: " & licensedMachineCode & Environment.NewLine &
                           "当前: " & currentMachineCode
            Return False
        End If

        If Not String.Equals(expiryString, "Never", StringComparison.OrdinalIgnoreCase) Then
            Dim currentNetworkTime As Nullable(Of DateTime) = GetNetworkTime() ' <--- 调用重写后的函数
            If Not currentNetworkTime.HasValue Then
                errorMessage = "无法从任何网络时间服务器获取准确时间。请检查您的网络连接后重试。"
                Return False
            End If

            Dim expiryDate As DateTime
            If DateTime.TryParseExact(expiryString, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, expiryDate) Then
                If currentNetworkTime.Value.Date > expiryDate.Date Then
                    errorMessage = "许可证已于 " & expiryDate.ToString("yyyy-MM-dd") & " 过期。"
                    Return False
                End If
            Else
                errorMessage = "许可证中的有效期格式无效 (应为 yyyy-MM-dd 或 Never)。"
                Return False
            End If
        End If
        errorMessage = "许可证有效。"
        Return True
    End Function

    Private Function GetNetworkTime() As Nullable(Of DateTime)
        Using cts As New CancellationTokenSource()
            Dim tasks As New List(Of Task(Of Nullable(Of DateTime)))
            For Each ntpServerHost As String In NtpServers
                tasks.Add(Task.Run(Function() QueryNtpServer(ntpServerHost, cts.Token), cts.Token))
            Next

            While tasks.Count > 0
                Try

                    Dim completedTask As Task(Of Nullable(Of DateTime)) = Task.WhenAny(tasks).Result


                    tasks.Remove(completedTask)


                    Dim networkTime As Nullable(Of DateTime) = completedTask.Result
                    If networkTime.HasValue Then
                        cts.Cancel()

                        Return networkTime
                    End If
                Catch ex As OperationCanceledException
                    Exit While
                Catch ex As Exception

                End Try
            End While
            Return Nothing
        End Using
    End Function

    Private Function QueryNtpServer(host As String, token As CancellationToken) As Nullable(Of DateTime)
        Try

            Using udpClient As New UdpClient()

                udpClient.Client.SendTimeout = 2000
                udpClient.Client.ReceiveTimeout = 2000


                udpClient.Connect(host, 123)


                Dim ntpData As Byte() = New Byte(47) {}

                ntpData(0) = &H1B


                udpClient.Send(ntpData, ntpData.Length)


                If token.IsCancellationRequested Then Return Nothing


                Dim remoteEP As IPEndPoint = Nothing
                Dim serverReply As Byte() = udpClient.Receive(remoteEP)


                If token.IsCancellationRequested Then Return Nothing


                Dim intPart As ULong = CType(serverReply(40), ULong) << 24 Or
                                       CType(serverReply(41), ULong) << 16 Or
                                       CType(serverReply(42), ULong) << 8 Or
                                       CType(serverReply(43), ULong)

                Dim fractPart As ULong = CType(serverReply(44), ULong) << 24 Or
                                         CType(serverReply(45), ULong) << 16 Or
                                         CType(serverReply(46), ULong) << 8 Or
                                         CType(serverReply(47), ULong)

                Dim milliseconds As Long = (intPart * 1000) + ((fractPart * 1000) / &H100000000L)


                Dim networkDateTime As DateTime = (New DateTime(1900, 1, 1, 0, 0, 0, DateTimeKind.Utc)).AddMilliseconds(milliseconds)

                Return networkDateTime

            End Using
        Catch ex As SocketException

            Return Nothing
        Catch ex As Exception

            Return Nothing
        End Try
    End Function

    Private Function ParseLicenseValue(data As String, key As String) As String
        If String.IsNullOrWhiteSpace(data) OrElse String.IsNullOrWhiteSpace(key) Then
            Return Nothing
        End If

        Try
            Dim pattern As String = "(?i)" & System.Text.RegularExpressions.Regex.Escape(key) & "=([^;]*)"
            Dim match As System.Text.RegularExpressions.Match = System.Text.RegularExpressions.Regex.Match(data, pattern)

            If match.Success Then
                Return match.Groups(1).Value.Trim()
            Else
                Return Nothing
            End If
        Catch ex As ArgumentException
            Return Nothing
        End Try
    End Function

    Public Sub StoreLicense(serialNumber As String)
        If String.IsNullOrWhiteSpace(serialNumber) Then Return

        Try
            Dim regPath As String = Path.Combine(RegistryBaseKey, CompanyName, ProductName)
            Using key As RegistryKey = Registry.CurrentUser.CreateSubKey(regPath)
                If key IsNot Nothing Then
                    key.SetValue(LicenseValueName, serialNumber, RegistryValueKind.String)
                End If
            End Using
        Catch ex As Exception
            Throw New LicenseStorageException("无法将许可证密钥保存到注册表。", ex)
        End Try
    End Sub

    Public Function RetrieveLicense() As String
        Try
            Dim regPath As String = Path.Combine(RegistryBaseKey, CompanyName, ProductName)
            Using key As RegistryKey = Registry.CurrentUser.OpenSubKey(regPath, False)
                If key IsNot Nothing Then
                    Dim value = key.GetValue(LicenseValueName, Nothing)
                    Return If(TypeOf value Is String, CStr(value), Nothing)
                End If
            End Using
        Catch ex As Exception
            ' 在实际产品中，应记录错误日志
        End Try
        Return Nothing
    End Function

    Public Sub ClearStoredLicense()
        Try
            Dim regPath As String = Path.Combine(RegistryBaseKey, CompanyName, ProductName)
            Using key As RegistryKey = Registry.CurrentUser.OpenSubKey(regPath, True)
                If key IsNot Nothing Then
                    Try
                        key.DeleteValue(LicenseValueName, False)
                    Catch exValue As Exception
                        Throw New LicenseStorageException("无法删除注册表值 '" & LicenseValueName & "'。", exValue)
                    End Try
                End If
            End Using
        Catch ex As Exception
            Throw New LicenseStorageException("无法打开或操作注册表项以清除许可证。", ex)
        End Try
    End Sub

    Public Class LicenseStorageException
        Inherits Exception
        Public Sub New(message As String, innerException As Exception)
            MyBase.New(message, innerException)
        End Sub
        Public Sub New(message As String)
            MyBase.New(message)
        End Sub
    End Class

End Module